import React, { MouseEvent, ReactNode } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { BsThreeDots } from 'react-icons/bs';
import { BiSolidChevronRight } from 'react-icons/bi';
import { PLACEMENT, TRIGGER_TYPE } from 'baseui/popover';
import StatefulPopover from '@/pages/editor/StatefulPopover';
import { StyledButton } from '@/pages/editor/file/styleObject';
import { FileAction } from './FileItem';
import { FileStore, FilePermission } from '@/utils/graphEditor/data';
import { FolderIcon, MoveIcon } from '@/pages/home/<USER>';

export interface FileActionsPopoverProps {
  file: FileStore;
  actions: FileAction[];
  onFileMenuOpen?: (isOpen: boolean) => void;
  showMoveFile?: boolean;
  userFolders?: FileStore[];
  isOwner?: boolean;
  onMoveFile?: (fileId: string, parentFileId: string | null) => void;
  children?: ReactNode;
  placement?: string;
  ignoreBoundary?: boolean;
}

export function FileActionsPopover({
  file,
  actions,
  onFileMenuOpen,
  showMoveFile = false,
  userFolders = [],
  isOwner = false,
  onMoveFile,
  children,
  placement = 'bottom',
  ignoreBoundary = true,
}: FileActionsPopoverProps) {
  const [css, theme] = useStyletron();
  const maxMilliSeconds = Math.pow(2, 31) - 1;

  const displayActions = actions.filter((action) => !action.hidden);

  const defaultTrigger = (
    <Button size="compact" kind="text" shape="square">
      <BsThreeDots title="actions" size={20} color={theme.colors['icon.primary']} />
    </Button>
  );

  if (displayActions.length === 0 && !showMoveFile) {
    return null;
  }

  return (
    <StatefulPopover
      onOpen={() => {
        onFileMenuOpen?.(true);
      }}
      onClose={() => {
        onFileMenuOpen?.(false);
      }}
      // @ts-ignore
      onClick={(e) => {
        e.stopPropagation();
      }}
      ignoreBoundary={ignoreBoundary}
      animateOutTime={100}
      showArrow={false}
      placement={placement}
      overrides={{
        Body: {
          style: ({ $theme }) => ({
            width: '102px',
            zIndex: 1,
          }),
        },
        Inner: {
          style: ({ $theme }) => ({
            paddingTop: '8px',
            paddingBottom: '8px',
            paddingLeft: '0px',
            paddingRight: '0px',
          }),
        },
      }}
      content={({ close }) => (
        <>
          {displayActions.map((action) => (
            <FileItemAction key={action.label} action={action} close={close} />
          ))}
          {showMoveFile && onMoveFile && (
            <StyledButton
              disabled={!isOwner}
              onClick={(e: MouseEvent<HTMLButtonElement>) => {
                e.stopPropagation();
              }}
            >
              <StatefulPopover
                showArrow={false}
                placement={isOwner ? PLACEMENT.rightTop : PLACEMENT.right}
                triggerType={TRIGGER_TYPE.hover}
                overrides={
                  isOwner
                    ? {
                        Inner: {
                          style: {
                            paddingTop: '0px',
                            paddingRight: '0px',
                            paddingLeft: '0px',
                            paddingBottom: '0px',
                            color: theme.colors['dropdown.text'],
                          },
                        },
                      }
                    : { Inner: { style: { color: theme.colors['dropdown.text'] } } }
                }
                onMouseLeaveDelay={isOwner ? maxMilliSeconds : 200}
                content={() => {
                  return isOwner ? (
                    <div>
                      {userFolders.length > 0 && (
                        <>
                          <div
                            className={css({
                              fontSize: '12px',
                              fontWeight: 'bold',
                              paddingTop: '8px',
                              paddingBottom: '8px',
                              paddingRight: '8px',
                              paddingLeft: '8px',
                              color: theme.colors['dropdown.text'],
                            })}
                          >
                            Folders
                          </div>
                          {userFolders.map((folder) => {
                            return (
                              <div
                                key={folder.id}
                                className={css({
                                  fontSize: '12px',
                                  cursor: 'pointer',
                                  height: '30px',
                                  display: 'flex',
                                  paddingLeft: '8px',
                                  paddingRight: '8px',
                                  backgroundColor: theme.colors['dropdown.background'],
                                  ':hover': {
                                    backgroundColor: theme.colors['dropdown.background.hover'],
                                  },
                                })}
                                onClick={() => {
                                  onMoveFile(file.id, folder.id);
                                  close();
                                }}
                              >
                                <div
                                  className={css({
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '12px',
                                  })}
                                >
                                  <FolderIcon /> {folder.name}
                                </div>
                              </div>
                            );
                          })}
                        </>
                      )}
                      {file.parent_id && (
                        <div
                          onClick={() => {
                            onMoveFile(file.id, null);
                          }}
                          className={css({
                            fontSize: '12px',
                            paddingLeft: '8px',
                            paddingTop: '8px',
                            paddingBottom: '8px',
                            paddingRight: '8px',
                            borderTop: `1px solid ${theme.colors['dropdown.border']}`,
                            cursor: 'pointer',
                            backgroundColor: theme.colors['dropdown.background'],
                            ':hover': {
                              backgroundColor: theme.colors['dropdown.background.hover'],
                            },
                          })}
                        >
                          Remove from folder
                        </div>
                      )}
                    </div>
                  ) : (
                    'You are not the owner of this file'
                  );
                }}
              >
                <div
                  className={css({ fontSize: '14px', height: '32px', display: 'flex', alignItems: 'center' })}
                >
                  <span className={css({ opacity: !isOwner ? 0.5 : 1 })}>
                    <MoveIcon />
                  </span>
                  <span className={css({ marginLeft: '8px' })}>Move</span>
                  <BiSolidChevronRight />
                </div>
              </StatefulPopover>
            </StyledButton>
          )}
        </>
      )}
    >
      {children || defaultTrigger}
    </StatefulPopover>
  );
}

function FileItemAction({ action, close }: { action: FileAction; close: () => void }) {
  const [css, theme] = useStyletron();

  return (
    <StyledButton
      key={action.label}
      disabled={action.disabled}
      onClick={(e: MouseEvent<HTMLButtonElement>) => {
        if (action.handleFn) {
          action.handleFn();
          close();
        }
        e.stopPropagation();
      }}
    >
      <div
        className={css({
          fontSize: '14px',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          opacity: action.disabled ? 0.5 : 1,
        })}
      >
        <span>{action.icon}</span>
        <span
          className={css({
            marginLeft: '8px',
            color: action.label === 'Delete' ? theme.colors['dropdown.text.error'] : 'inherit',
          })}
        >
          {action.label}
        </span>
        {action.popoverContent ? (
          <StatefulPopover
            content={action.popoverContent}
            placement={PLACEMENT.rightTop}
            autoFocus={false}
            showArrow={false}
          >
            <div
              className={css({
                position: 'absolute',
                left: 0,
                right: 0,
                bottom: 0,
                top: 0,
              })}
            />
          </StatefulPopover>
        ) : null}
      </div>
    </StyledButton>
  );
}
