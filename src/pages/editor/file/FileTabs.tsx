import { Tab, Tabs } from 'baseui/tabs-motion';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { MouseEvent, useCallback, useMemo, useRef, useState } from 'react';
import { ActiveFile, FileChangeType, FilePermission, FileStore } from '@/utils/graphEditor/data';
import { useMutation, useQuery } from 'react-query';
import { CodeEditor } from '@/pages/editor/file/CodeEditor';
import { FileTabsStyleObject, FileTabStyleObject } from '@/pages/editor/file/styleObject';
import { BaseSkeleton } from '@/components/BaseSkeleton';
import { Button } from '@tigergraph/app-ui-lib/button';
import { AddIconNoCircle, DeleteIcon, RenameIcon } from '@/pages/home/<USER>';
import IconButton from '@/components/IconButton.tsx';
import { MdKeyboardArrowLeft, MdKeyboardArrowRight } from 'react-icons/md';
import { createFileReq, getUserFile, getGithubFile, deleteFile, renameFileReq } from '@/pages/editor/file/api';
import ConfirmModal from '@/components/ConfirmModal';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { useSaveFileMutation } from '@/pages/editor/file/hooks';
import { Spinner } from '@tigergraph/app-ui-lib/spinner';
import { showToast } from '@/components/styledToasterContainer';
import { IoClose } from 'react-icons/io5';
import { FaSave } from 'react-icons/fa';
import { useTheme } from '@/contexts/themeContext';
import EditorShortcuts from '@/pages/editor/file/EditorShortcuts';
import { forwardRef, useImperativeHandle } from 'react';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import { FileActionsPopover, FileAction } from './FileActionsPopover';
import { getErrorMessage } from '@/utils/utils';
import { AxiosError } from 'axios';

interface FileTabsProps {
  onCreateFile: (file: { is_folder: boolean; content?: string; parentId?: string; name?: string }) => void;
  onChangeFile: (type: FileChangeType, file: FileStore) => void;
  onSaveAll: () => Promise<void>;
  updateFileStore: (type: FileChangeType, file: FileStore) => void;
}

export interface FileTabsRef {
  saveAllFiles: () => Promise<void>;
}

export const FileTabs = forwardRef<FileTabsRef, FileTabsProps>(function FileTabs(
  { onCreateFile, onChangeFile, updateFileStore, onSaveAll },
  ref
) {
  const [css, theme] = useStyletron();
  const { themeType } = useTheme();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<ActiveFile | null>(null);
  const [isRenaming, setIsRenaming] = useState<string | null>(null);
  const [tempName, setTempName] = useState<string>('');

  const { activeFiles, setActiveFiles, setCurrentFileId, currentFileId, unsavedFiles, selectedCode, runCmd } =
    useEditorContext();
  const { currentWorkspace, currentGraph } = useWorkspaceContext();

  let tabIdToClose = useRef<string>('');

  const activeFile = activeFiles.find((file) => file.id === currentFileId);
  const activeIndex = activeFiles.findIndex((file) => file.id === currentFileId);

  const isEditable = useMemo(() => {
    return activeFile?.permission === FilePermission.Edit || activeFile?.permission === FilePermission.Owner;
  }, [activeFile]);

  // code in the code editor
  const code = activeFile?.content || '';
  // code saved to backend
  const sourceCode = activeFile?.sourceCode;
  const setCode = useCallback(
    (code: string) => {
      setActiveFiles((files) => files.map((file) => (file.id === currentFileId ? { ...file, content: code } : file)));
    },
    [currentFileId, setActiveFiles]
  );
  const setSourceCode = useCallback(
    (code: string) => {
      setActiveFiles((files) =>
        files.map((file) => (file.id === currentFileId ? { ...file, sourceCode: code } : file))
      );
    },
    [currentFileId, setActiveFiles]
  );

  const { isFetching } = useQuery(
    ['fileInfo', activeFile],
    async () => {
      const file = activeFile!;
      if (file.type === 'UserFile') {
        const res = await getUserFile(file.id);
        return res.content;
      } else if (file.type === 'Tutorial') {
        const res = await getGithubFile(file.tutorialType!, file.name);
        const binaryString = atob(res.content);
        const uint8Array = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          uint8Array[i] = binaryString.charCodeAt(i);
        }

        const textDecoder = new TextDecoder('utf-8');
        const decodedText = textDecoder.decode(uint8Array);
        return decodedText;
      }
      throw new Error('Invalid file type');
    },
    {
      onSuccess: (data) => {
        setCode(data);
        setSourceCode(data);
      },
      enabled: !!(activeFile && activeFile.sourceCode === undefined && !activeFile.is_temp),
    }
  );

  const handleCloseFileTab = useCallback((id: string) => {
    const files = activeFiles.filter((f) => f.id !== id);
    if (!files.length) {
      setCurrentFileId('');
    } else if (id === currentFileId) {
      const index = activeFiles.findIndex((f) => f.id === id);
      if (index >= activeFiles.length - 1) {
        setCurrentFileId(files[files.length - 1].id);
      } else {
        setCurrentFileId(files[index].id);
      }
    }

    setActiveFiles(files);
  }, [activeFiles, currentFileId, setActiveFiles, setCurrentFileId]);

  const handleActiveTabChange = (activeKey: string) => {
    setCurrentFileId(activeKey);
  };

  const handlePrevOrNextTab = (direction: number) => {
    let nextActiveIndex = activeIndex + direction;
    nextActiveIndex = Math.max(0, Math.min(nextActiveIndex, activeFiles.length - 1));
    setCurrentFileId(activeFiles[nextActiveIndex].id);
  };

  const handleCodeChange = useCallback(
    (code: string) => {
      setCode(code);
    },
    [setCode]
  );

  const handleRunAll = useCallback(() => {
    let modifiedCode = code.trim();
    if (modifiedCode === '') {
      return;
    }
    runCmd(currentWorkspace!, currentGraph, modifiedCode);
  }, [code, currentGraph, currentWorkspace, runCmd]);

  const handleRunSelection = useCallback(() => {
    runCmd(currentWorkspace!, currentGraph, selectedCode);
  }, [currentGraph, currentWorkspace, runCmd, selectedCode]);

  const { mutateAsync: createFile } = useMutation(['create', 'file'], createFileReq);

  const [isSavingAll, setIsSavingAll] = useState(false);
  const { mutateAsync: saveFileMutate, status } = useSaveFileMutation();
  const isSaving = status === 'loading' || isSavingAll;

  const { mutateAsync: deleteFileMutate } = useMutation(['delete', 'file'], deleteFile, {
    onSuccess: () => {
      showToast({ kind: 'positive', message: 'File deleted successfully' });
    },
    onError: (error: AxiosError) => {
      showToast({ kind: 'negative', message: getErrorMessage(error) });
    },
  });

  const { mutateAsync: renameFileMutate } = useMutation(['rename', 'file'], renameFileReq, {
    onSuccess: () => {
      showToast({ kind: 'positive', message: 'File renamed successfully' });
    },
    onError: (error: AxiosError) => {
      showToast({ kind: 'negative', message: getErrorMessage(error) });
    },
  });

  const saveFile = useCallback(
    async (data: { fileId: string; content: string }, disableToast: boolean = false) => {
      await saveFileMutate(data, {
        onSuccess(data) {
          const { updated_at, id, content } = data.Result!;
          onChangeFile(FileChangeType.UPDATE, {
            id,
            updated_at,
            content,
          } as FileStore);
          !disableToast && showToast({ kind: 'positive', message: 'File saved successfully' });
        },
      });
    },
    [onChangeFile, saveFileMutate]
  );

  const handleSaveFile = useCallback(
    async (file: ActiveFile, code: string, disableToast: boolean = false) => {
      if (file.is_temp) {
        if (!file.file_id) {
          // the tab is not save yet, create a new file
          await createFile(
            { is_folder: false, content: code, name: file.name, parent_id: null },
            {
              onSuccess(data) {
                const _file = data.Result!;
                // set temp file's fileId
                setActiveFiles((files) =>
                  files.map((f) =>
                    f.id === file.id ? { ...f, file_id: _file.id, content: code, sourceCode: code } : f
                  )
                );
                updateFileStore(FileChangeType.CREATE, _file);
                disableToast && showToast({ kind: 'positive', message: 'File saved successfully' });
              },
            }
          );
        } else {
          await saveFile({ fileId: file.file_id, content: code }, disableToast);
        }
        return;
      }

      await saveFile({ fileId: file.id, content: code }, disableToast);
    },
    [createFile, saveFile, setActiveFiles, updateFileStore]
  );

  const saveAllFiles = useCallback(async () => {
    setIsSavingAll(true);
    try {
      await Promise.all(unsavedFiles.map((file) => handleSaveFile(file, file.content, true)));
    } finally {
      setIsSavingAll(false);
    }
  }, [unsavedFiles, handleSaveFile]);

  useImperativeHandle(ref, () => ({
    saveAllFiles,
  }));

  const saveCurrentFile = useCallback(() => {
    if (!activeFile) {
      return;
    }
    handleSaveFile(activeFile, activeFile.content);
  }, [activeFile, handleSaveFile]);

  const handleDeleteFile = useCallback(async (file: ActiveFile) => {
    try {
      await deleteFileMutate(file.id);
      // Close the tab if it's open
      handleCloseFileTab(file.id);
      // Update the file store
      onChangeFile(FileChangeType.DELETE, file as FileStore);
    } catch (error) {
      // Error is handled by the mutation's onError
    }
  }, [deleteFileMutate, handleCloseFileTab, onChangeFile]);

  const handleRenameFile = useCallback(async (file: ActiveFile, newName: string) => {
    try {
      const response = await renameFileMutate({ fileId: file.id, name: newName });
      const updatedFile = response.Result!;

      // Update active files
      setActiveFiles(prev => prev.map(f =>
        f.id === file.id ? { ...f, name: newName } : f
      ));

      // Update the file store
      onChangeFile(FileChangeType.UPDATE, updatedFile);
    } catch (error) {
      // Error is handled by the mutation's onError
    }
  }, [renameFileMutate, setActiveFiles, onChangeFile]);

  const fileTabTile = (file: ActiveFile, index: number) => {
    const { name: fileName, id: fileId } = file;
    const isActive = fileId === currentFileId;
    const unsavedFile = unsavedFiles.find((file) => file.id === fileId);
    const unsaved = !!unsavedFile;

    // Convert ActiveFile to FileStore for compatibility with FileActionsPopover
    const fileAsStore: FileStore = {
      ...file,
      permission: file.is_temp ? FilePermission.Owner : (file.permission || FilePermission.Edit),
    };

    const isOwner = fileAsStore.permission === FilePermission.Owner || file.is_temp;

    const fileActions: FileAction[] = [
      {
        label: 'Save',
        handleFn: () => {
          if (unsavedFile) {
            handleSaveFile(file, unsavedFile.content);
          }
        },
        disabled: !unsaved,
        hidden: false,
        icon: <FaSave size={16} color={theme.colors['icon.primary']} />,
      },
      {
        label: 'Rename',
        handleFn: () => {
          setIsRenaming(fileId);
          setTempName(fileName);
        },
        disabled: !isOwner,
        hidden: !isOwner,
        icon: <RenameIcon />,
      },
      {
        label: 'Delete',
        handleFn: () => {
          setFileToDelete(file);
          setIsDeleteModalOpen(true);
        },
        disabled: !isOwner,
        hidden: !isOwner,
        icon: <DeleteIcon />,
      },
    ];

    const displayActions = fileActions.filter((action) => !action.hidden);

    return (
      <div
        className={
          css({
            minWidth: '50px',
            display: 'flex',
            position: 'relative',
            alignItems: 'center',
            whiteSpace: 'nowrap',
          }) + (isActive ? ' file-tab-active' : '')
        }
      >
        <span>{fileName}</span>
        {unsaved ? <span>*</span> : <span>&nbsp;&nbsp;</span>}
        <div className={css({ marginLeft: '8px', display: 'flex', alignItems: 'center', gap: '2px' })}>
          {!isSaving && unsaved && (
            <IconButton
              onClick={() => {
                handleSaveFile(file, unsavedFile.content);
              }}
              $style={{ height: '14px' }}
            >
              <FaSave size={14} color={theme.colors['icon.primary']} />
            </IconButton>
          )}
          {isActive && isSaving ? (
            <Spinner $color={theme.colors.gray1000} $size={'14px'} $borderWidth={'2px'} />
          ) : (
            <IconButton
              data-testid="close-file-tab"
              $style={{ height: '16px' }}
              onClick={(event: MouseEvent<HTMLButtonElement>) => {
                event.stopPropagation();
                if (!unsaved) {
                  handleCloseFileTab(fileId);
                  return;
                }
                setShowConfirmDialog(true);
                tabIdToClose.current = fileId;
              }}
            >
              <IoClose size={16} color={theme.colors['tabs.icon']} />
            </IconButton>
          )}
          {displayActions.length > 0 && (
            <FileActionsPopover
              file={fileAsStore}
              actions={displayActions}
              showMoveFile={false}
              userFolders={[]}
              isOwner={isOwner}
              onMoveFile={() => { }}
              placement="bottomLeft"
              ignoreBoundary={false}
            />
          )}
        </div>
      </div>
    );
  };

  return (
    <div
      className={css({
        height: '100%',
        width: '100%',
        flexGrow: 1,
        position: 'relative',
      })}
    >
      {!isFetching && (!activeFiles || activeFiles.length === 0) && (
        <div
          className={css({
            width: '100%',
            height: '100%',
          })}
        >
          <div
            className={css({
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: 'calc(100% - 60px)',
              overflowY: 'auto',
              flexDirection: 'column',
            })}
          >
            <div
              className={css({
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
                alignItems: 'center',
                maxWidth: '422px',
                paddingBottom: '24px',
                marginBottom: '24px',
                borderBottom: `1px solid ${theme.colors.divider}`,
                fontSize: '12px',
                textAlign: 'center',
              })}
            >
              You have not created or selected a GSQL file yet. Please select a GSQL file from the list on the left or
              create a new GSQL file.
              <Button
                onClick={() => {
                  onCreateFile({ is_folder: false });
                }}
                kind="secondary"
                size="compact"
              >
                <AddIconNoCircle />
                Create New GSQL File
              </Button>
            </div>
            <EditorShortcuts />
          </div>
        </div>
      )}
      {(isFetching || (activeFiles && activeFiles.length > 0)) && (
        <div
          className={css({
            position: 'relative',
            height: '100%',
          })}
        >
          <div
            className={css({
              width: '16px',
              position: 'absolute',
              height: '32px',
              top: '8px',
              left: '0px',
              borderBottom: `1px solid ${theme.colors.divider}`,
              borderRadius: '0',
            })}
          >
            <IconButton
              data-testid="prev-file-tab"
              className={css({
                width: '100%',
                height: '100%',
                display: activeIndex === 0 ? 'none' : 'block',
              })}
              onClick={() => {
                handlePrevOrNextTab(-1);
              }}
            >
              <MdKeyboardArrowLeft />
            </IconButton>
          </div>
          <Tabs
            activeKey={currentFileId}
            onChange={({ activeKey }) => {
              handleActiveTabChange(activeKey as string);
            }}
            overrides={FileTabsStyleObject(theme)}
          >
            {activeFiles.map((file, index) => {
              return (
                <Tab key={file.id} overrides={FileTabStyleObject(theme)} title={fileTabTile(file, index)}>
                  <div
                    className={css({
                      height: '100%',
                      display: 'flex',
                      position: 'relative',
                      zIndex: 0,
                    })}
                  >
                    {isFetching && (
                      <div
                        className={css({
                          display: 'flex',
                          flexDirection: 'column',
                          gap: '24px',
                          paddingTop: '24px',
                          paddingLeft: '24px',
                        })}
                      >
                        <BaseSkeleton width={'527px'} height={'16px'} />
                        <BaseSkeleton width={'335px'} height={'16px'} />
                        <BaseSkeleton width={'527px'} height={'16px'} />
                        <BaseSkeleton width={'335px'} height={'16px'} />
                        <BaseSkeleton width={'241px'} height={'16px'} />
                        <BaseSkeleton width={'417px'} height={'16px'} />
                      </div>
                    )}
                    {!isFetching && (
                      <CodeEditor
                        onCodeChange={handleCodeChange}
                        onSaveFile={saveCurrentFile}
                        onSaveAll={onSaveAll}
                        value={code}
                        editable={isEditable}
                      />
                    )}
                  </div>
                </Tab>
              );
            })}
          </Tabs>
          <div
            className={css({
              width: '16px',
              position: 'absolute',
              height: '32px',
              top: '8px',
              right: '0px',
              borderBottom: `1px solid ${theme.colors.divider}`,
              borderRadius: '0',
            })}
          >
            <IconButton
              data-testid="next-file-tab"
              className={css({
                width: '100%',
                height: '100%',
                display: activeIndex === activeFiles.length - 1 ? 'none' : 'block',
              })}
              onClick={() => {
                handlePrevOrNextTab(1);
              }}
            >
              <MdKeyboardArrowRight />
            </IconButton>
          </div>
        </div>
      )}
      <ConfirmModal
        open={showConfirmDialog}
        header="Warning"
        body={'You have unsaved changes that will be lost. Do you still want to close?'}
        onConfirm={() => {
          handleCloseFileTab(tabIdToClose.current);
          setShowConfirmDialog(false);
        }}
        onCancel={() => {
          setShowConfirmDialog(false);
        }}
      />
      <ConfirmModal
        open={isDeleteModalOpen}
        header="Delete File"
        body={`Are you sure you want to delete "${fileToDelete?.name}"? This action cannot be undone.`}
        onConfirm={() => {
          if (fileToDelete) {
            handleDeleteFile(fileToDelete);
          }
          setIsDeleteModalOpen(false);
          setFileToDelete(null);
        }}
        onCancel={() => {
          setIsDeleteModalOpen(false);
          setFileToDelete(null);
        }}
      />
    </div>
  );
});
